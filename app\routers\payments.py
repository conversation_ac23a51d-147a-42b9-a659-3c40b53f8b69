from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user

router = APIRouter()

fake_payments_db = []
fake_payment_methods = ["credit_card", "paypal", "bank_transfer"]

class PaymentInitiate(BaseModel):
    order_id: int
    method: str
    amount: float

class Payment(BaseModel):
    id: int
    order_id: int
    user_email: str
    method: str
    amount: float
    status: str

@router.post("/initiate", response_model=Payment, tags=["Payments"])
async def initiate_payment(payment: PaymentInitiate, current_user: dict = Depends(get_current_user)):
    payment_id = len(fake_payments_db) + 1
    new_payment = {
        "id": payment_id,
        "order_id": payment.order_id,
        "user_email": current_user["email"],
        "method": payment.method,
        "amount": payment.amount,
        "status": "pending"
    }
    fake_payments_db.append(new_payment)
    return new_payment

@router.get("/status", tags=["Payments"])
async def payment_status(payment_id: int, current_user: dict = Depends(get_current_user)):
    for payment in fake_payments_db:
        if payment["id"] == payment_id and payment["user_email"] == current_user["email"]:
            return {"id": payment_id, "status": payment["status"]}
    raise HTTPException(status_code=404, detail="Payment not found")

@router.post("/verify", tags=["Payments"])
async def verify_payment(payment_id: int, current_user: dict = Depends(get_current_user)):
    for payment in fake_payments_db:
        if payment["id"] == payment_id and payment["user_email"] == current_user["email"]:
            payment["status"] = "verified"
            return {"msg": "Payment verified", "payment": payment}
    raise HTTPException(status_code=404, detail="Payment not found")

@router.post("/refund", tags=["Payments"])
async def refund_payment(payment_id: int, current_user: dict = Depends(get_current_user)):
    for payment in fake_payments_db:
        if payment["id"] == payment_id and payment["user_email"] == current_user["email"]:
            payment["status"] = "refunded"
            return {"msg": "Payment refunded", "payment": payment}
    raise HTTPException(status_code=404, detail="Payment not found")

@router.get("/methods", response_model=List[str], tags=["Payments"])
async def list_payment_methods(current_user: dict = Depends(get_current_user)):
    return fake_payment_methods 
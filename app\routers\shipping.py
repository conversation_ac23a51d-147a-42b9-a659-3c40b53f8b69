from fastapi import APIRouter, Depends
from pydantic import BaseModel
from app.routers.auth import get_current_user

router = APIRouter()

class ShippingRequest(BaseModel):
    address: str
    weight: float

class ShippingCost(BaseModel):
    cost: float
    currency: str = "USD"

@router.post("/calculate", response_model=ShippingCost, tags=["Shipping"])
async def calculate_shipping(req: ShippingRequest, current_user: dict = Depends(get_current_user)):
    # Simple demo: $5 base + $2 per kg
    cost = 5 + 2 * req.weight
    return {"cost": cost, "currency": "USD"} 
from fastapi import FastAPI

# Import routers from app.routers
from app.routers.auth import router as auth_router
from app.routers.users import router as users_router
from app.routers.products import router as products_router
from app.routers.categories import router as categories_router
from app.routers.brands import router as brands_router
from app.routers.inventory import router as inventory_router
from app.routers.cart import router as cart_router
from app.routers.wishlist import router as wishlist_router
from app.routers.orders import router as orders_router
from app.routers.payments import router as payments_router
from app.routers.addresses import router as addresses_router
from app.routers.shipping import router as shipping_router
from app.routers.reviews import router as reviews_router
from app.routers.ratings import router as ratings_router
from app.routers.coupons import router as coupons_router
from app.routers.notifications import router as notifications_router
from app.routers.uploads import router as uploads_router
from app.routers.support import router as support_router
# Import admin routers
from app.routers.admin_auth import router as admin_auth_router
from app.routers.admin_users import router as admin_users_router
from app.routers.admin_products import router as admin_products_router
from app.routers.admin_categories_brands import router as admin_categories_brands_router
from app.routers.admin_inventory import router as admin_inventory_router
from app.routers.admin_orders import router as admin_orders_router
from app.routers.admin_payments import router as admin_payments_router
from app.routers.admin_shipping import router as admin_shipping_router
from app.routers.admin_customers import router as admin_customers_router
from app.routers.admin_coupons import router as admin_coupons_router
from app.routers.admin_reviews import router as admin_reviews_router
from app.routers.admin_notifications import router as admin_notifications_router
from app.routers.admin_reports import router as admin_reports_router
from app.routers.admin_settings import router as admin_settings_router
from app.routers.admin_logs import router as admin_logs_router

def create_app() -> FastAPI:
    app = FastAPI(title="E-Commerce API", version="1.0.0")
    app.include_router(auth_router, prefix="/auth", tags=["Auth"])
    app.include_router(users_router, prefix="/users", tags=["Users"])
    app.include_router(products_router, prefix="/products", tags=["Products"])
    app.include_router(categories_router, prefix="/categories", tags=["Categories"])
    app.include_router(brands_router, prefix="/brands", tags=["Brands"])
    app.include_router(inventory_router, prefix="/inventory", tags=["Inventory"])
    app.include_router(cart_router, prefix="/cart", tags=["Cart"])
    app.include_router(wishlist_router, prefix="/wishlist", tags=["Wishlist"])
    app.include_router(orders_router, prefix="/orders", tags=["Orders"])
    app.include_router(payments_router, prefix="/payments", tags=["Payments"])
    app.include_router(addresses_router, prefix="/addresses", tags=["Addresses"])
    app.include_router(shipping_router, prefix="/shipping", tags=["Shipping"])
    app.include_router(reviews_router, prefix="/reviews", tags=["Reviews"])
    app.include_router(ratings_router, prefix="/ratings", tags=["Ratings"])
    app.include_router(coupons_router, prefix="/coupons", tags=["Coupons"])
    app.include_router(notifications_router, prefix="/notifications", tags=["Notifications"])
    app.include_router(uploads_router, prefix="/upload", tags=["Uploads"])
    app.include_router(support_router, prefix="/support", tags=["Support"])
    # Admin routers
    app.include_router(admin_auth_router, prefix="/admin/auth", tags=["Admin Auth"])
    app.include_router(admin_users_router, prefix="/admin/users", tags=["Admin Users"])
    app.include_router(admin_products_router, prefix="/admin/products", tags=["Admin Products"])
    app.include_router(admin_categories_brands_router, prefix="/admin", tags=["Admin Categories", "Admin Brands"])
    app.include_router(admin_inventory_router, prefix="/admin/inventory", tags=["Admin Inventory"])
    app.include_router(admin_orders_router, prefix="/admin/orders", tags=["Admin Orders"])
    app.include_router(admin_payments_router, prefix="/admin/payments", tags=["Admin Payments"])
    app.include_router(admin_shipping_router, prefix="/admin/shipping", tags=["Admin Shipping"])
    app.include_router(admin_customers_router, prefix="/admin/customers", tags=["Admin Customers"])
    app.include_router(admin_coupons_router, prefix="/admin/coupons", tags=["Admin Coupons"])
    app.include_router(admin_reviews_router, prefix="/admin/reviews", tags=["Admin Reviews"])
    app.include_router(admin_notifications_router, prefix="/admin/notifications", tags=["Admin Notifications"])
    app.include_router(admin_reports_router, prefix="/admin/reports", tags=["Admin Reports"])
    app.include_router(admin_settings_router, prefix="/admin/settings", tags=["Admin Settings"])
    app.include_router(admin_logs_router, prefix="/admin/logs", tags=["Admin Logs"])
    return app

app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("hello:app", host="0.0.0.0", port=8000, reload=True)

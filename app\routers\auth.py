from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.security import <PERSON>A<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime, timedelta
import jwt

SECRET_KEY = "123456789987654321"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

router = APIRouter()

# In-memory user store for demo
fake_users_db = {}

class UserSignUp(BaseModel):
    first_name: str
    last_name: str
    phone: str
    email: EmailStr
    password: str

class UserOut(BaseModel):
    email: EmailStr
    is_verified: bool = False
    first_name: str
    last_name: str
    phone: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_password(plain_password, hashed_password):
    return plain_password == hashed_password  # Replace with real hash check

def get_user(email: str):
    return fake_users_db.get(email)

def authenticate_user(email: str, password: str):
    user = get_user(email)
    if not user or not verify_password(password, user["password"]):
        return False
    return user

def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        user = get_user(email)
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")
        return user
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

@router.post("/sign-up", response_model=UserOut, tags=["Auth Sign Up"])
async def sign_up(user: UserSignUp):
    if user.email in fake_users_db:
        raise HTTPException(status_code=400, detail="Email already registered")
    fake_users_db[user.email] = {
        "email": user.email,
        "password": user.password,
        "is_verified": False,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "phone": user.phone
    }
    # In real app: send verification email
    return {
        "email": user.email,
        "is_verified": False,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "phone": user.phone
    }

@router.post("/login", response_model=Token, tags=["Auth Login"])
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect email or password")
    access_token = create_access_token(data={"sub": user["email"]})
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    # In real app: blacklist token or manage session
    return {"msg": "Logged out"}

@router.post("/refresh", response_model=Token)
async def refresh(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM], options={"verify_exp": False})
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        new_token = create_access_token(data={"sub": email})
        return {"access_token": new_token, "token_type": "bearer"}
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

@router.post("/forgot-password")
async def forgot_password(request: ForgotPasswordRequest):
    # In real app: send reset link
    if request.email not in fake_users_db:
        raise HTTPException(status_code=404, detail="Email not found")
    return {"msg": "Password reset link sent"}

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    new_password: str

@router.post("/reset-password")
async def reset_password(request: ResetPasswordRequest):
    user = get_user(request.email)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    user["password"] = request.new_password
    return {"msg": "Password reset successful"}

class VerifyEmailRequest(BaseModel):
    email: EmailStr

@router.post("/verify-email")
async def verify_email(request: VerifyEmailRequest):
    user = get_user(request.email)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    user["is_verified"] = True
    return {"msg": "Email verified"} 
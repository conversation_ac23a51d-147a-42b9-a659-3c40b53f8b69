from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user

router = APIRouter()

fake_reviews_db = []

class ReviewCreate(BaseModel):
    product_id: int
    rating: int
    comment: str

class Review(BaseModel):
    id: int
    user_email: str
    product_id: int
    rating: int
    comment: str

@router.post("/add", response_model=Review, tags=["Reviews"])
async def add_review(review: ReviewCreate, current_user: dict = Depends(get_current_user)):
    review_id = len(fake_reviews_db) + 1
    new_review = {
        "id": review_id,
        "user_email": current_user["email"],
        "product_id": review.product_id,
        "rating": review.rating,
        "comment": review.comment
    }
    fake_reviews_db.append(new_review)
    return new_review

@router.get("/product/{product_id}", response_model=List[Review], tags=["Reviews"])
async def get_product_reviews(product_id: int):
    return [r for r in fake_reviews_db if r["product_id"] == product_id]

@router.get("/user/{user_id}", response_model=List[Review], tags=["Reviews"])
async def get_user_reviews(user_id: str):
    return [r for r in fake_reviews_db if r["user_email"] == user_id]

@router.put("/{id}/edit", response_model=Review, tags=["Reviews"])
async def edit_review(id: int, review: ReviewCreate, current_user: dict = Depends(get_current_user)):
    for r in fake_reviews_db:
        if r["id"] == id and r["user_email"] == current_user["email"]:
            r["product_id"] = review.product_id
            r["rating"] = review.rating
            r["comment"] = review.comment
            return r
    raise HTTPException(status_code=404, detail="Review not found")

@router.delete("/{id}/delete", tags=["Reviews"])
async def delete_review(id: int, current_user: dict = Depends(get_current_user)):
    for i, r in enumerate(fake_reviews_db):
        if r["id"] == id and r["user_email"] == current_user["email"]:
            fake_reviews_db.pop(i)
            return {"msg": "Review deleted"}
    raise HTTPException(status_code=404, detail="Review not found") 
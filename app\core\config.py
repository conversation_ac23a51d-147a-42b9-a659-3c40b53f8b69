import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Security settings
SECRET_KEY = os.getenv("SECRET_KEY", "123456789987654321")
ALGORITHM = os.getenv("ALG<PERSON><PERSON>H<PERSON>", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))

# Database settings
DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable is required")
from fastapi import APIRouter, Depends, HTTPException
from typing import List
from app.routers.auth import get_current_user
from app.routers.products import fake_products_db, Product

router = APIRouter()

fake_brands_db = [
    {"id": 1, "name": "BrandA"},
    {"id": 2, "name": "BrandB"},
    {"id": 3, "name": "BrandC"},
]

@router.get("", tags=["Brands"])
async def list_brands(current_user: dict = Depends(get_current_user)):
    return fake_brands_db

@router.get("/{id}", tags=["Brands"])
async def get_brand(id: int, current_user: dict = Depends(get_current_user)):
    for brand in fake_brands_db:
        if brand["id"] == id:
            return brand
    raise HTTPException(status_code=404, detail="Brand not found")

@router.get("/{id}/products", response_model=List[Product], tags=["Brands"])
async def get_products_by_brand(id: int, current_user: dict = Depends(get_current_user)):
    # For demo, assign products to brands by id
    return [p for p in fake_products_db if p["id"] % 3 + 1 == id] 
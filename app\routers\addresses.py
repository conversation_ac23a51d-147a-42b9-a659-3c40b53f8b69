from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.routers.auth import get_current_user

router = APIRouter()

fake_addresses_db = {}

class Address(BaseModel):
    id: int
    user_email: str
    address: str
    is_default: bool = False

class AddressCreate(BaseModel):
    address: str

class AddressUpdate(BaseModel):
    id: int
    address: str

class AddressDelete(BaseModel):
    id: int

class SetDefaultRequest(BaseModel):
    id: int

@router.get("", response_model=List[Address], tags=["Addresses"])
async def list_addresses(current_user: dict = Depends(get_current_user)):
    return fake_addresses_db.get(current_user["email"], [])

@router.post("/add", response_model=Address, tags=["Addresses"])
async def add_address(addr: AddressCreate, current_user: dict = Depends(get_current_user)):
    addresses = fake_addresses_db.setdefault(current_user["email"], [])
    addr_id = len(addresses) + 1
    address = {"id": addr_id, "user_email": current_user["email"], "address": addr.address, "is_default": len(addresses) == 0}
    addresses.append(address)
    return address

@router.put("/update", response_model=Address, tags=["Addresses"])
async def update_address(addr: AddressUpdate, current_user: dict = Depends(get_current_user)):
    addresses = fake_addresses_db.setdefault(current_user["email"], [])
    for address in addresses:
        if address["id"] == addr.id:
            address["address"] = addr.address
            return address
    raise HTTPException(status_code=404, detail="Address not found")

@router.delete("/delete", tags=["Addresses"])
async def delete_address(addr: AddressDelete, current_user: dict = Depends(get_current_user)):
    addresses = fake_addresses_db.setdefault(current_user["email"], [])
    for i, address in enumerate(addresses):
        if address["id"] == addr.id:
            addresses.pop(i)
            return {"msg": "Address deleted"}
    raise HTTPException(status_code=404, detail="Address not found")

@router.post("/set-default", tags=["Addresses"])
async def set_default_address(req: SetDefaultRequest, current_user: dict = Depends(get_current_user)):
    addresses = fake_addresses_db.setdefault(current_user["email"], [])
    found = False
    for address in addresses:
        address["is_default"] = (address["id"] == req.id)
        if address["id"] == req.id:
            found = True
    if not found:
        raise HTTPException(status_code=404, detail="Address not found")
    return {"msg": "Default address set"} 
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict
from app.routers.auth import get_current_user, fake_users_db

router = APIRouter()

class UserProfile(BaseModel):
    email: EmailStr
    is_verified: bool = False
    language: Optional[str] = "en"
    currency: Optional[str] = "USD"

class UpdateProfileRequest(BaseModel):
    language: Optional[str]
    currency: Optional[str]

class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

class PreferencesRequest(BaseModel):
    language: Optional[str]
    currency: Optional[str]

@router.get("/me", response_model=UserProfile)
async def get_profile(current_user: dict = Depends(get_current_user)):
    return {
        "email": current_user["email"],
        "is_verified": current_user.get("is_verified", False),
        "language": current_user.get("language", "en"),
        "currency": current_user.get("currency", "USD"),
    }

@router.put("/update-profile", response_model=UserProfile)
async def update_profile(update: UpdateProfileRequest, current_user: dict = Depends(get_current_user)):
    if update.language:
        current_user["language"] = update.language
    if update.currency:
        current_user["currency"] = update.currency
    return {
        "email": current_user["email"],
        "is_verified": current_user.get("is_verified", False),
        "language": current_user.get("language", "en"),
        "currency": current_user.get("currency", "USD"),
    }

@router.put("/change-password")
async def change_password(data: ChangePasswordRequest, current_user: dict = Depends(get_current_user)):
    if data.old_password != current_user["password"]:
        raise HTTPException(status_code=400, detail="Old password is incorrect")
    current_user["password"] = data.new_password
    return {"msg": "Password changed successfully"}

@router.get("/preferences")
async def get_preferences(current_user: dict = Depends(get_current_user)):
    return {
        "language": current_user.get("language", "en"),
        "currency": current_user.get("currency", "USD"),
    }

@router.put("/preferences")
async def update_preferences(prefs: PreferencesRequest, current_user: dict = Depends(get_current_user)):
    if prefs.language:
        current_user["language"] = prefs.language
    if prefs.currency:
        current_user["currency"] = prefs.currency
    return {
        "language": current_user.get("language", "en"),
        "currency": current_user.get("currency", "USD"),
    }

@router.delete("/delete-account")
async def delete_account(current_user: dict = Depends(get_current_user)):
    email = current_user["email"]
    if email in fake_users_db:
        del fake_users_db[email]
    return {"msg": "Account deleted"} 
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, EmailStr
from typing import Optional

router = APIRouter()

class AdminLoginRequest(BaseModel):
    email: EmailStr
    password: str

class AdminToken(BaseModel):
    access_token: str
    token_type: str

class AdminOut(BaseModel):
    id: int
    email: EmailStr
    role: str

@router.post("/login", response_model=AdminToken, tags=["Admin Auth"])
async def admin_login(data: AdminLoginRequest):
    # Demo: always succeed
    return {"access_token": "admin-token", "token_type": "bearer"}

@router.post("/logout", tags=["Admin Auth"])
async def admin_logout():
    return {"msg": "Admin logged out"}

@router.post("/refresh", response_model=AdminToken, tags=["Admin Auth"])
async def admin_refresh():
    return {"access_token": "admin-token", "token_type": "bearer"}

@router.get("/me", response_model=AdminOut, tags=["Admin Auth"])
async def admin_me():
    return {"id": 1, "email": "<EMAIL>", "role": "admin"} 
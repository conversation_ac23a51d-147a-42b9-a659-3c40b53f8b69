from sqlmodel import SQLModel, <PERSON>
from typing import Optional
from datetime import datetime

class CartItem(SQLModel, table=True):
    __tablename__ = "cart_items"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    product_id: int = Field(foreign_key="products.id")
    quantity: int = Field(gt=0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

class Ticket:
    def __init__(self, id: int, user_email: str, subject: str, messages: list, status: str = "open"):
        self.id = id
        self.user_email = user_email
        self.subject = subject
        self.messages = messages
        self.status = status

class ChatMessage:
    def __init__(self, user_email: str, message: str):
        self.user_email = user_email
        self.message = message 
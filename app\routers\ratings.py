from fastapi import APIRouter
from app.routers.reviews import fake_reviews_db

router = APIRouter()

@router.get("/average/{product_id}", tags=["Ratings"])
async def average_rating(product_id: int):
    ratings = [r["rating"] for r in fake_reviews_db if r["product_id"] == product_id]
    if not ratings:
        return {"product_id": product_id, "average": None}
    return {"product_id": product_id, "average": sum(ratings) / len(ratings)} 
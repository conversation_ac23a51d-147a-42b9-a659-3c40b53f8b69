[project]
name = "e-commerce"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = []

[tool.poetry]
name = "e-commerce"
version = "0.1.0"
description = "Advanced E-Commerce FastAPI Backend"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
fastapi = "^0.110.0"
uvicorn = {extras = ["standard"], version = "^0.29.0"}

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

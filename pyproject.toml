[project]
name = "ecommerce-app"
version = "0.1.0"
description = "Advanced E-Commerce FastAPI Backend"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.110.0",
    "uvicorn[standard]>=0.29.0",
    "pyjwt>=2.8.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "email-validator>=2.0.0",
    "python-multipart>=0.0.6",
    "sqlmodel>=0.0.14",
    "asyncpg>=0.29.0",
    "python-dotenv>=1.0.0",
    "alembic>=1.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

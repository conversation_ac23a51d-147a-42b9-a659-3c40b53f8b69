from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user

router = APIRouter()

fake_notifications_db = {}

class Notification(BaseModel):
    id: int
    user_email: str
    message: str
    read: bool = False

class NotificationSend(BaseModel):
    user_email: str
    message: str

@router.get("", response_model=List[Notification], tags=["Notifications"])
async def list_notifications(current_user: dict = Depends(get_current_user)):
    return fake_notifications_db.get(current_user["email"], [])

@router.get("/unread", response_model=List[Notification], tags=["Notifications"])
async def unread_notifications(current_user: dict = Depends(get_current_user)):
    return [n for n in fake_notifications_db.get(current_user["email"], []) if not n["read"]]

@router.post("/mark-read", tags=["Notifications"])
async def mark_read(notification_id: int, current_user: dict = Depends(get_current_user)):
    for n in fake_notifications_db.get(current_user["email"], []):
        if n["id"] == notification_id:
            n["read"] = True
            return {"msg": "Notification marked as read"}
    raise HTTPException(status_code=404, detail="Notification not found")

@router.post("/clear", tags=["Notifications"])
async def clear_notifications(current_user: dict = Depends(get_current_user)):
    fake_notifications_db[current_user["email"]] = []
    return {"msg": "Notifications cleared"}

# Admin endpoints
@router.post("/admin/notifications/send", tags=["Admin Notifications"])
async def admin_send_notification(data: NotificationSend, current_user: dict = Depends(get_current_user)):
    notifications = fake_notifications_db.setdefault(data.user_email, [])
    notification_id = len(notifications) + 1
    notification = {"id": notification_id, "user_email": data.user_email, "message": data.message, "read": False}
    notifications.append(notification)
    return {"msg": "Notification sent", "notification": notification}

@router.post("/admin/notifications/settings", tags=["Admin Notifications"])
async def admin_notification_settings(settings: dict, current_user: dict = Depends(get_current_user)):
    # For demo, just echo settings
    return {"msg": "Notification settings updated", "settings": settings} 
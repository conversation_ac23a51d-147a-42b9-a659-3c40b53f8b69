from pydantic import BaseModel, EmailStr
from typing import Optional

class AdminUserSchema(BaseModel):
    id: int
    email: EmailStr
    role: str

class AdminUserCreateSchema(BaseModel):
    email: EmailStr
    password: str
    role: str

class AdminUserUpdateSchema(BaseModel):
    email: Optional[EmailStr] = None
    role: Optional[str] = None

class AdminRoleSchema(BaseModel):
    id: int
    name: str 
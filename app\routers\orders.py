from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.routers.auth import get_current_user

router = APIRouter()

fake_orders_db = []

class OrderCreate(BaseModel):
    items: List[int]  # product IDs
    shipping_address: str
    payment_method: str

class Order(BaseModel):
    id: int
    user_email: str
    items: List[int]
    shipping_address: str
    payment_method: str
    status: str

@router.post("/create", response_model=Order, tags=["Orders"])
async def create_order(order: OrderCreate, current_user: dict = Depends(get_current_user)):
    order_id = len(fake_orders_db) + 1
    new_order = {
        "id": order_id,
        "user_email": current_user["email"],
        "items": order.items,
        "shipping_address": order.shipping_address,
        "payment_method": order.payment_method,
        "status": "pending"
    }
    fake_orders_db.append(new_order)
    return new_order

@router.get("", response_model=List[Order], tags=["Orders"])
async def get_orders(current_user: dict = Depends(get_current_user)):
    return [o for o in fake_orders_db if o["user_email"] == current_user["email"]]

@router.get("/{id}", response_model=Order, tags=["Orders"])
async def get_order(id: int, current_user: dict = Depends(get_current_user)):
    for order in fake_orders_db:
        if order["id"] == id and order["user_email"] == current_user["email"]:
            return order
    raise HTTPException(status_code=404, detail="Order not found")

@router.get("/{id}/status", tags=["Orders"])
async def get_order_status(id: int, current_user: dict = Depends(get_current_user)):
    for order in fake_orders_db:
        if order["id"] == id and order["user_email"] == current_user["email"]:
            return {"id": id, "status": order["status"]}
    raise HTTPException(status_code=404, detail="Order not found")

@router.post("/{id}/cancel", tags=["Orders"])
async def cancel_order(id: int, current_user: dict = Depends(get_current_user)):
    for order in fake_orders_db:
        if order["id"] == id and order["user_email"] == current_user["email"]:
            if order["status"] == "cancelled":
                raise HTTPException(status_code=400, detail="Order already cancelled")
            order["status"] = "cancelled"
            return {"msg": "Order cancelled", "order": order}
    raise HTTPException(status_code=404, detail="Order not found")

@router.get("/history", response_model=List[Order], tags=["Orders"])
async def order_history(current_user: dict = Depends(get_current_user)):
    return [o for o in fake_orders_db if o["user_email"] == current_user["email"]] 
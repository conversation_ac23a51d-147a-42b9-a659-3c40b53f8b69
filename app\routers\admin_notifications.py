from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminNotification(BaseModel):
    id: int
    message: str
    read: bool

class NotificationSend(BaseModel):
    message: str

class NotificationSettings(BaseModel):
    preferences: dict

@router.get("", response_model=List[AdminNotification], tags=["Admin Notifications"])
async def list_notifications():
    return []

@router.post("/send", tags=["Admin Notifications"])
async def send_notification(notification: NotificationSend):
    return {"msg": "Notification sent"}

@router.get("/{id}", response_model=AdminNotification, tags=["Admin Notifications"])
async def get_notification(id: int):
    return {"id": id, "message": "Sample notification", "read": False}

@router.put("/{id}/update", tags=["Admin Notifications"])
async def update_notification(id: int, notification: NotificationSend):
    return {"msg": "Notification updated"}

@router.delete("/{id}/delete", tags=["Admin Notifications"])
async def delete_notification(id: int):
    return {"msg": "Notification deleted"}

@router.post("/settings", tags=["Admin Notifications"])
async def manage_preferences(settings: NotificationSettings):
    return {"msg": "Preferences updated"} 
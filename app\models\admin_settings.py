class GeneralSettings:
    def __init__(self, site_name: str, maintenance_mode: bool):
        self.site_name = site_name
        self.maintenance_mode = maintenance_mode

class CurrencySettings:
    def __init__(self, currency: str):
        self.currency = currency

class ThemeSettings:
    def __init__(self, theme: str):
        self.theme = theme

class Banner:
    def __init__(self, id: int, url: str):
        self.id = id
        self.url = url 
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminCategory(BaseModel):
    id: int
    name: str

class AdminBrand(BaseModel):
    id: int
    name: str

@router.get("/categories", response_model=List[AdminCategory], tags=["Admin Categories"])
async def list_categories():
    return []

@router.post("/categories/create", response_model=AdminCategory, tags=["Admin Categories"])
async def create_category(category: AdminCategory):
    return category

@router.put("/categories/{id}/update", response_model=AdminCategory, tags=["Admin Categories"])
async def update_category(id: int, category: AdminCategory):
    return category

@router.delete("/categories/{id}/delete", tags=["Admin Categories"])
async def delete_category(id: int):
    return {"msg": "Category deleted"}

@router.get("/brands", response_model=List[AdminBrand], tags=["Admin Brands"])
async def list_brands():
    return []

@router.post("/brands/create", response_model=AdminBrand, tags=["Admin Brands"])
async def create_brand(brand: AdminBrand):
    return brand

@router.put("/brands/{id}/update", response_model=AdminBrand, tags=["Admin Brands"])
async def update_brand(id: int, brand: AdminBrand):
    return brand

@router.delete("/brands/{id}/delete", tags=["Admin Brands"])
async def delete_brand(id: int):
    return {"msg": "Brand deleted"} 
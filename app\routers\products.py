from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter()

# In-memory product store for demo
fake_products_db = [
    {"id": 1, "name": "Laptop", "category_id": 1, "price": 1200.0, "tags": ["electronics", "computers"]},
    {"id": 2, "name": "Smartphone", "category_id": 1, "price": 800.0, "tags": ["electronics", "phones"]},
    {"id": 3, "name": "T-shirt", "category_id": 2, "price": 20.0, "tags": ["clothing"]},
    {"id": 4, "name": "Sneakers", "category_id": 2, "price": 60.0, "tags": ["clothing", "shoes"]},
    {"id": 5, "name": "Coffee Maker", "category_id": 3, "price": 100.0, "tags": ["kitchen"]},
]

class Product(BaseModel):
    id: int
    name: str
    category_id: int
    price: float
    tags: List[str]

@router.get("", response_model=List[Product])
async def list_products():
    return fake_products_db

@router.get("/{id}", response_model=Product)
async def get_product(id: int):
    for product in fake_products_db:
        if product["id"] == id:
            return product
    raise HTTPException(status_code=404, detail="Product not found")

@router.get("/search", response_model=List[Product])
async def search_products(q: Optional[str] = Query(None, description="Search query")):
    if not q:
        return fake_products_db
    return [p for p in fake_products_db if q.lower() in p["name"].lower() or any(q.lower() in tag for tag in p["tags"])]

@router.get("/recommendations", response_model=List[Product])
async def recommendations():
    # For demo, return first 3 products as recommendations
    return fake_products_db[:3]

@router.get("/{id}/related", response_model=List[Product])
async def related_products(id: int):
    product = next((p for p in fake_products_db if p["id"] == id), None)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    # For demo, related products are those in the same category, excluding itself
    return [p for p in fake_products_db if p["category_id"] == product["category_id"] and p["id"] != id] 
from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
import json

class Product(SQLModel, table=True):
    __tablename__ = "products"

    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(index=True)
    description: Optional[str] = Field(default=None)
    price: float = Field(gt=0)
    category_id: int = Field(foreign_key="categories.id")
    brand_id: Optional[int] = Field(default=None, foreign_key="brands.id")
    tags: str = Field(default="[]")  # Store as JSON string
    stock_quantity: int = Field(default=0, ge=0)
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)

    # Helper methods for tags
    def get_tags(self) -> List[str]:
        return json.loads(self.tags) if self.tags else []

    def set_tags(self, tags: List[str]):
        self.tags = json.dumps(tags)
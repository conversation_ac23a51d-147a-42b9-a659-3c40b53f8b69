from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.routers.auth import get_current_user

router = APIRouter()

fake_coupons_db = [
    {"code": "SAVE10", "discount": 10, "active": True},
    {"code": "FREESHIP", "discount": 0, "active": True},
]

class Coupon(BaseModel):
    code: str
    discount: int
    active: bool

class CouponApply(BaseModel):
    code: str

@router.get("", response_model=List[Coupon], tags=["Coupons"])
async def get_coupons(current_user: dict = Depends(get_current_user)):
    return fake_coupons_db

@router.post("/validate", tags=["Coupons"])
async def validate_coupon(data: CouponApply, current_user: dict = Depends(get_current_user)):
    for coupon in fake_coupons_db:
        if coupon["code"] == data.code and coupon["active"]:
            return {"valid": True, "discount": coupon["discount"]}
    return {"valid": False}

@router.post("/apply", tags=["Coupons"])
async def apply_coupon(data: CouponApply, current_user: dict = Depends(get_current_user)):
    # For demo, just return applied
    return {"msg": f"Coupon {data.code} applied"}

@router.post("/remove", tags=["Coupons"])
async def remove_coupon(data: CouponApply, current_user: dict = Depends(get_current_user)):
    return {"msg": f"Coupon {data.code} removed"}

# Admin endpoints
class CouponAdmin(BaseModel):
    code: str
    discount: int
    active: bool = True

@router.post("/admin/coupons/create", tags=["Admin Coupons"])
async def admin_create_coupon(coupon: CouponAdmin, current_user: dict = Depends(get_current_user)):
    fake_coupons_db.append(coupon.dict())
    return {"msg": "Coupon created", "coupon": coupon}

@router.put("/admin/coupons/update", tags=["Admin Coupons"])
async def admin_update_coupon(coupon: CouponAdmin, current_user: dict = Depends(get_current_user)):
    for c in fake_coupons_db:
        if c["code"] == coupon.code:
            c["discount"] = coupon.discount
            c["active"] = coupon.active
            return {"msg": "Coupon updated", "coupon": c}
    raise HTTPException(status_code=404, detail="Coupon not found")

@router.delete("/admin/coupons/delete", tags=["Admin Coupons"])
async def admin_delete_coupon(data: CouponApply, current_user: dict = Depends(get_current_user)):
    for i, c in enumerate(fake_coupons_db):
        if c["code"] == data.code:
            fake_coupons_db.pop(i)
            return {"msg": "Coupon deleted"}
    raise HTTPException(status_code=404, detail="Coupon not found") 
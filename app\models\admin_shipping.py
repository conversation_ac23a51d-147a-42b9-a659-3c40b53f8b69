class ShippingProvider:
    def __init__(self, id: int, name: str):
        self.id = id
        self.name = name

class ShippingZone:
    def __init__(self, id: int, name: str):
        self.id = id
        self.name = name

class ShippingRateRequest:
    def __init__(self, provider_id: int, zone_id: int, weight: float):
        self.provider_id = provider_id
        self.zone_id = zone_id
        self.weight = weight 
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.products import fake_products_db, Product

router = APIRouter()

# In-memory category store for demo
fake_categories_db = [
    {"id": 1, "name": "Electronics"},
    {"id": 2, "name": "Clothing"},
    {"id": 3, "name": "Kitchen"},
]

class Category(BaseModel):
    id: int
    name: str

@router.get("", response_model=List[Category])
async def list_categories():
    return fake_categories_db

@router.get("/{id}", response_model=Category)
async def get_category(id: int):
    for category in fake_categories_db:
        if category["id"] == id:
            return category
    raise HTTPException(status_code=404, detail="Category not found")

@router.get("/{id}/products", response_model=List[Product])
async def category_products(id: int):
    # Return all products in the given category
    return [p for p in fake_products_db if p["category_id"] == id] 
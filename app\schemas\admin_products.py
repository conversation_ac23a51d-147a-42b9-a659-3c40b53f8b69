from pydantic import BaseModel
from typing import Optional, List

class AdminProductSchema(BaseModel):
    id: int
    name: str
    price: float

class AdminProductCreateSchema(BaseModel):
    name: str
    price: float

class AdminProductUpdateSchema(BaseModel):
    name: Optional[str] = None
    price: Optional[float] = None

class ProductImageSchema(BaseModel):
    url: str

class ProductVariantSchema(BaseModel):
    id: int
    name: str 
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminCoupon(BaseModel):
    id: int
    code: str
    discount: int

@router.get("", response_model=List[AdminCoupon], tags=["Admin Coupons"])
async def list_coupons():
    return []

@router.post("/create", response_model=AdminCoupon, tags=["Admin Coupons"])
async def create_coupon(coupon: AdminCoupon):
    return coupon

@router.get("/{id}", response_model=AdminCoupon, tags=["Admin Coupons"])
async def get_coupon(id: int):
    return {"id": id, "code": "SAVE10", "discount": 10}

@router.put("/{id}/update", response_model=AdminCoupon, tags=["Admin Coupons"])
async def update_coupon(id: int, coupon: AdminCoupon):
    return coupon

@router.delete("/{id}/delete", tags=["Admin Coupons"])
async def delete_coupon(id: int):
    return {"msg": "Coupon deleted"}

@router.get("/usage-report", tags=["Admin Coupons"])
async def usage_report():
    return {"msg": "Usage analytics"} 
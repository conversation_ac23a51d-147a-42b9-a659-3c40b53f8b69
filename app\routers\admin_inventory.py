from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class Stock(BaseModel):
    product_id: int
    quantity: int

class StockHistory(BaseModel):
    product_id: int
    changes: List[int]

class StockAdjustment(BaseModel):
    adjustment: int

@router.get("", response_model=List[Stock], tags=["Admin Inventory"])
async def list_stock():
    return []

@router.get("/{product_id}", response_model=Stock, tags=["Admin Inventory"])
async def get_stock(product_id: int):
    return {"product_id": product_id, "quantity": 0}

@router.put("/{product_id}/update", response_model=Stock, tags=["Admin Inventory"])
async def update_stock(product_id: int, stock: Stock):
    return stock

@router.get("/{product_id}/history", response_model=StockHistory, tags=["Admin Inventory"])
async def stock_history(product_id: int):
    return {"product_id": product_id, "changes": []}

@router.post("/{product_id}/adjust", tags=["Admin Inventory"])
async def adjust_stock(product_id: int, adjustment: StockAdjustment):
    return {"msg": "Stock adjusted"} 
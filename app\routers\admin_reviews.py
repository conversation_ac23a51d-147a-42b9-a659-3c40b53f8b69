from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminReview(BaseModel):
    id: int
    content: str
    status: str

@router.get("", response_model=List[AdminReview], tags=["Admin Reviews"])
async def list_reviews():
    return []

@router.get("/{id}", response_model=AdminReview, tags=["Admin Reviews"])
async def get_review(id: int):
    return {"id": id, "content": "Sample review", "status": "pending"}

@router.post("/{id}/approve", tags=["Admin Reviews"])
async def approve_review(id: int):
    return {"msg": "Review approved"}

@router.post("/{id}/reject", tags=["Admin Reviews"])
async def reject_review(id: int):
    return {"msg": "Review rejected"}

@router.delete("/{id}/delete", tags=["Admin Reviews"])
async def delete_review(id: int):
    return {"msg": "Review deleted"} 
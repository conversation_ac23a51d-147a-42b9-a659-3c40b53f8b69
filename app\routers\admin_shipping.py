from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class ShippingProvider(BaseModel):
    id: int
    name: str

class ShippingZone(BaseModel):
    id: int
    name: str

class ShippingRateRequest(BaseModel):
    provider_id: int
    zone_id: int
    weight: float

@router.get("/providers", response_model=List[ShippingProvider], tags=["Admin Shipping"])
async def list_providers():
    return []

@router.post("/providers/add", response_model=ShippingProvider, tags=["Admin Shipping"])
async def add_provider(provider: ShippingProvider):
    return provider

@router.put("/providers/{id}/update", response_model=ShippingProvider, tags=["Admin Shipping"])
async def update_provider(id: int, provider: ShippingProvider):
    return provider

@router.delete("/providers/{id}/delete", tags=["Admin Shipping"])
async def delete_provider(id: int):
    return {"msg": "Provider deleted"}

@router.get("/zones", response_model=List[ShippingZone], tags=["Admin Shipping"])
async def list_zones():
    return []

@router.post("/zones/create", response_model=ShippingZone, tags=["Admin Shipping"])
async def create_zone(zone: ShippingZone):
    return zone

@router.put("/zones/{id}/update", response_model=ShippingZone, tags=["Admin Shipping"])
async def update_zone(id: int, zone: ShippingZone):
    return zone

@router.delete("/zones/{id}/delete", tags=["Admin Shipping"])
async def delete_zone(id: int):
    return {"msg": "Zone deleted"}

@router.post("/rates/calculate", tags=["Admin Shipping"])
async def calculate_rate(rate: ShippingRateRequest):
    return {"msg": "Rate calculated"} 
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from pydantic import BaseModel
from typing import List

router = APIRouter()

class GeneralSettings(BaseModel):
    site_name: str
    maintenance_mode: bool

class CurrencySettings(BaseModel):
    currency: str

class ThemeSettings(BaseModel):
    theme: str

class Banner(BaseModel):
    id: int
    url: str

@router.get("", response_model=GeneralSettings, tags=["Admin Settings"])
async def get_settings():
    return {"site_name": "My Shop", "maintenance_mode": False}

@router.put("/update", response_model=GeneralSettings, tags=["Admin Settings"])
async def update_settings(settings: GeneralSettings):
    return settings

@router.get("/currency", response_model=CurrencySettings, tags=["Admin Settings"])
async def get_currency():
    return {"currency": "USD"}

@router.put("/currency", response_model=CurrencySettings, tags=["Admin Settings"])
async def update_currency(settings: CurrencySettings):
    return settings

@router.get("/themes", response_model=ThemeSettings, tags=["Admin Settings"])
async def get_theme():
    return {"theme": "light"}

@router.put("/themes", response_model=ThemeSettings, tags=["Admin Settings"])
async def update_theme(settings: ThemeSettings):
    return settings

@router.get("/banners", response_model=List[Banner], tags=["Admin Settings"])
async def list_banners():
    return []

@router.post("/banners/upload", tags=["Admin Settings"])
async def upload_banner(file: UploadFile = File(...)):
    return {"msg": "Banner uploaded"}

@router.delete("/banners/delete", tags=["Admin Settings"])
async def delete_banner(banner_id: int):
    return {"msg": "Banner deleted"} 
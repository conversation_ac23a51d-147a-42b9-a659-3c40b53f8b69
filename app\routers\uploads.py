from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from app.routers.auth import get_current_user

router = APIRouter()

fake_uploads_db = {}

@router.post("/avatar", tags=["Uploads"])
async def upload_avatar(file: UploadFile = File(...), current_user: dict = Depends(get_current_user)):
    fake_uploads_db.setdefault(current_user["email"], {})["avatar"] = file.filename
    return {"msg": "Avatar uploaded", "filename": file.filename}

@router.post("/document", tags=["Uploads"])
async def upload_document(file: UploadFile = File(...), current_user: dict = Depends(get_current_user)):
    docs = fake_uploads_db.setdefault(current_user["email"], {}).setdefault("documents", [])
    docs.append(file.filename)
    return {"msg": "Document uploaded", "filename": file.filename}

@router.post("/product-image", tags=["Uploads"])
async def upload_product_image(file: UploadFile = File(...), current_user: dict = Depends(get_current_user)):
    imgs = fake_uploads_db.setdefault(current_user["email"], {}).setdefault("product_images", [])
    imgs.append(file.filename)
    return {"msg": "Product image uploaded", "filename": file.filename}

@router.delete("/remove", tags=["Uploads"])
async def remove_upload(filename: str, current_user: dict = Depends(get_current_user)):
    user_uploads = fake_uploads_db.get(current_user["email"], {})
    for key in ["avatar", "documents", "product_images"]:
        if key == "avatar" and user_uploads.get("avatar") == filename:
            user_uploads.pop("avatar")
            return {"msg": "Avatar removed"}
        elif key in user_uploads and filename in user_uploads[key]:
            user_uploads[key].remove(filename)
            return {"msg": f"{key} file removed"}
    raise HTTPException(status_code=404, detail="File not found") 
# Core business logic for admin product management

def list_products():
    pass

def create_product(product_data):
    pass

def get_product(product_id):
    pass

def update_product(product_id, product_data):
    pass

def delete_product(product_id):
    pass

def add_product_image(product_id, image_data):
    pass

def remove_product_image(product_id, image_data):
    pass

def list_variants(product_id):
    pass

def add_variant(product_id, variant_data):
    pass

def remove_variant(product_id, variant_data):
    pass 
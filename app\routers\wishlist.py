from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user

router = APIRouter()

# In-memory wishlist per user (email as key)
fake_wishlist_db = {}

class WishlistItem(BaseModel):
    product_id: int

@router.get("", tags=["Wishlist"])
async def view_wishlist(current_user: dict = Depends(get_current_user)):
    wishlist = fake_wishlist_db.get(current_user["email"], [])
    return wishlist

@router.post("/add", tags=["Wishlist"])
async def add_to_wishlist(item: WishlistItem, current_user: dict = Depends(get_current_user)):
    wishlist = fake_wishlist_db.setdefault(current_user["email"], [])
    if item.product_id not in wishlist:
        wishlist.append(item.product_id)
    return {"msg": "Product added to wishlist", "wishlist": wishlist}

@router.delete("/remove", tags=["Wishlist"])
async def remove_from_wishlist(item: WishlistItem, current_user: dict = Depends(get_current_user)):
    wishlist = fake_wishlist_db.setdefault(current_user["email"], [])
    if item.product_id in wishlist:
        wishlist.remove(item.product_id)
        return {"msg": "Product removed from wishlist", "wishlist": wishlist}
    raise HTTPException(status_code=404, detail="Product not in wishlist") 
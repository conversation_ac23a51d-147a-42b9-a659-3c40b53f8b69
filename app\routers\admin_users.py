from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, EmailStr
from typing import List, Optional

router = APIRouter()

class AdminUser(BaseModel):
    id: int
    email: EmailStr
    role: str

class AdminUserCreate(BaseModel):
    email: EmailStr
    password: str
    role: str

class AdminUserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    role: Optional[str] = None

class AdminRole(BaseModel):
    id: int
    name: str

@router.get("", response_model=List[AdminUser], tags=["Admin Users"])
async def list_admin_users():
    return []

@router.post("/create", response_model=AdminUser, tags=["Admin Users"])
async def create_admin_user(user: AdminUserCreate):
    return {"id": 1, "email": user.email, "role": user.role}

@router.get("/{id}", response_model=AdminUser, tags=["Admin Users"])
async def get_admin_user(id: int):
    return {"id": id, "email": "<EMAIL>", "role": "admin"}

@router.put("/{id}/update", response_model=AdminUser, tags=["Admin Users"])
async def update_admin_user(id: int, user: AdminUserUpdate):
    return {"id": id, "email": user.email or "<EMAIL>", "role": user.role or "admin"}

@router.delete("/{id}/delete", tags=["Admin Users"])
async def delete_admin_user(id: int):
    return {"msg": "Admin user deleted"}

# Role management
@router.get("/roles", response_model=List[AdminRole], tags=["Admin Roles"])
async def list_roles():
    return []

@router.post("/roles/create", response_model=AdminRole, tags=["Admin Roles"])
async def create_role(role: AdminRole):
    return role

@router.put("/roles/{id}/update", response_model=AdminRole, tags=["Admin Roles"])
async def update_role(id: int, role: AdminRole):
    return role

@router.delete("/roles/{id}/delete", tags=["Admin Roles"])
async def delete_role(id: int):
    return {"msg": "Role deleted"} 
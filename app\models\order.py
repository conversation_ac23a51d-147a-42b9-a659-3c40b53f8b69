from sqlmodel import SQLModel, <PERSON>
from typing import Optional
from datetime import datetime
import json

class Order(SQLModel, table=True):
    __tablename__ = "orders"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    items: str = Field(default="[]")  # Store as JSON string
    shipping_address: str
    payment_method: str
    status: str = Field(default="pending")
    total_amount: float = Field(default=0.0, ge=0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None)
    
    # Helper methods for items
    def get_items(self) -> list:
        return json.loads(self.items) if self.items else []
    
    def set_items(self, items: list):
        self.items = json.dumps(items)

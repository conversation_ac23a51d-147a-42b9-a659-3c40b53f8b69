from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user

router = APIRouter()

fake_tickets_db = []
fake_chat_db = {}

class TicketCreate(BaseModel):
    subject: str
    message: str

class Ticket(BaseModel):
    id: int
    user_email: str
    subject: str
    messages: List[str]
    status: str

class TicketReply(BaseModel):
    message: str

@router.get("/tickets", response_model=List[Ticket], tags=["Support"])
async def list_tickets(current_user: dict = Depends(get_current_user)):
    return [t for t in fake_tickets_db if t["user_email"] == current_user["email"]]

@router.post("/tickets/create", response_model=Ticket, tags=["Support"])
async def create_ticket(ticket: TicketCreate, current_user: dict = Depends(get_current_user)):
    ticket_id = len(fake_tickets_db) + 1
    new_ticket = {
        "id": ticket_id,
        "user_email": current_user["email"],
        "subject": ticket.subject,
        "messages": [ticket.message],
        "status": "open"
    }
    fake_tickets_db.append(new_ticket)
    return new_ticket

@router.get("/tickets/{id}", response_model=Ticket, tags=["Support"])
async def get_ticket(id: int, current_user: dict = Depends(get_current_user)):
    for t in fake_tickets_db:
        if t["id"] == id and t["user_email"] == current_user["email"]:
            return t
    raise HTTPException(status_code=404, detail="Ticket not found")

@router.post("/tickets/{id}/reply", tags=["Support"])
async def reply_ticket(id: int, reply: TicketReply, current_user: dict = Depends(get_current_user)):
    for t in fake_tickets_db:
        if t["id"] == id and t["user_email"] == current_user["email"]:
            t["messages"].append(reply.message)
            return {"msg": "Reply added", "ticket": t}
    raise HTTPException(status_code=404, detail="Ticket not found")

@router.post("/tickets/{id}/close", tags=["Support"])
async def close_ticket(id: int, current_user: dict = Depends(get_current_user)):
    for t in fake_tickets_db:
        if t["id"] == id and t["user_email"] == current_user["email"]:
            t["status"] = "closed"
            return {"msg": "Ticket closed", "ticket": t}
    raise HTTPException(status_code=404, detail="Ticket not found")

# Live chat endpoints (demo, not real-time)
class ChatMessage(BaseModel):
    message: str

@router.get("/live-chat", tags=["Support"])
async def get_live_chat(current_user: dict = Depends(get_current_user)):
    return fake_chat_db.get(current_user["email"], [])

@router.post("/live-chat/messages", tags=["Support"])
async def send_chat_message(msg: ChatMessage, current_user: dict = Depends(get_current_user)):
    chat = fake_chat_db.setdefault(current_user["email"], [])
    chat.append(msg.message)
    return {"msg": "Message sent", "chat": chat} 
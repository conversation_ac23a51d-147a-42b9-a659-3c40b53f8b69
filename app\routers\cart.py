from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
from app.routers.auth import get_current_user
from app.routers.products import fake_products_db

router = APIRouter()

# In-memory cart per user (email as key)
fake_cart_db = {}

class CartItem(BaseModel):
    product_id: int
    quantity: int

@router.get("", tags=["Cart"])
async def view_cart(current_user: dict = Depends(get_current_user)):
    cart = fake_cart_db.get(current_user["email"], [])
    return cart

@router.post("/add", tags=["Cart"])
async def add_to_cart(item: CartItem, current_user: dict = Depends(get_current_user)):
    cart = fake_cart_db.setdefault(current_user["email"], [])
    for cart_item in cart:
        if cart_item["product_id"] == item.product_id:
            cart_item["quantity"] += item.quantity
            break
    else:
        cart.append({"product_id": item.product_id, "quantity": item.quantity})
    return {"msg": "Product added to cart", "cart": cart}

@router.put("/update", tags=["Cart"])
async def update_cart(item: CartItem, current_user: dict = Depends(get_current_user)):
    cart = fake_cart_db.setdefault(current_user["email"], [])
    for cart_item in cart:
        if cart_item["product_id"] == item.product_id:
            cart_item["quantity"] = item.quantity
            return {"msg": "Cart updated", "cart": cart}
    raise HTTPException(status_code=404, detail="Product not in cart")

@router.delete("/remove", tags=["Cart"])
async def remove_from_cart(item: CartItem, current_user: dict = Depends(get_current_user)):
    cart = fake_cart_db.setdefault(current_user["email"], [])
    for i, cart_item in enumerate(cart):
        if cart_item["product_id"] == item.product_id:
            cart.pop(i)
            return {"msg": "Product removed from cart", "cart": cart}
    raise HTTPException(status_code=404, detail="Product not in cart") 
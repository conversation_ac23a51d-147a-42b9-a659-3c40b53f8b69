from fastapi import APIRouter, Depends, HTTPException
from app.routers.auth import get_current_user

router = APIRouter()

fake_inventory_db = {
    1: 10,  # product_id: stock
    2: 0,
    3: 25,
    4: 5,
    5: 100,
}

@router.get("/{product_id}", tags=["Inventory"])
async def check_stock(product_id: int, current_user: dict = Depends(get_current_user)):
    stock = fake_inventory_db.get(product_id)
    if stock is None:
        raise HTTPException(status_code=404, detail="Product not found in inventory")
    return {"product_id": product_id, "stock": stock, "available": stock > 0} 
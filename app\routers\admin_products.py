from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminProduct(BaseModel):
    id: int
    name: str
    price: float

class AdminProductCreate(BaseModel):
    name: str
    price: float

class AdminProductUpdate(BaseModel):
    name: str = None
    price: float = None

class ProductImage(BaseModel):
    url: str

class ProductVariant(BaseModel):
    id: int
    name: str

@router.get("", response_model=List[AdminProduct], tags=["Admin Products"])
async def list_products():
    return []

@router.post("/create", response_model=AdminProduct, tags=["Admin Products"])
async def create_product(product: AdminProductCreate):
    return {"id": 1, "name": product.name, "price": product.price}

@router.get("/{id}", response_model=AdminProduct, tags=["Admin Products"])
async def get_product(id: int):
    return {"id": id, "name": "Sample Product", "price": 100.0}

@router.put("/{id}/update", response_model=AdminProduct, tags=["Admin Products"])
async def update_product(id: int, product: AdminProductUpdate):
    return {"id": id, "name": product.name or "Sample Product", "price": product.price or 100.0}

@router.delete("/{id}/delete", tags=["Admin Products"])
async def delete_product(id: int):
    return {"msg": "Product deleted"}

# Images
@router.post("/{id}/images/add", tags=["Admin Products"])
async def add_product_image(id: int, image: ProductImage):
    return {"msg": "Image added"}

@router.delete("/{id}/images/remove", tags=["Admin Products"])
async def remove_product_image(id: int, image: ProductImage):
    return {"msg": "Image removed"}

# Variants
@router.get("/{id}/variants", response_model=List[ProductVariant], tags=["Admin Products"])
async def list_variants(id: int):
    return []

@router.post("/{id}/variants/add", tags=["Admin Products"])
async def add_variant(id: int, variant: ProductVariant):
    return {"msg": "Variant added"}

@router.delete("/{id}/variants/remove", tags=["Admin Products"])
async def remove_variant(id: int, variant: ProductVariant):
    return {"msg": "Variant removed"} 
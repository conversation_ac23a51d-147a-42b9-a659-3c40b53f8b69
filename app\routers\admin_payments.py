from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class AdminPayment(BaseModel):
    id: int
    status: str

@router.get("", response_model=List[AdminPayment], tags=["Admin Payments"])
async def list_payments():
    return []

@router.get("/{id}", response_model=AdminPayment, tags=["Admin Payments"])
async def get_payment(id: int):
    return {"id": id, "status": "completed"}

@router.get("/{id}/status", tags=["Admin Payments"])
async def payment_status(id: int):
    return {"id": id, "status": "completed"}

@router.post("/{id}/verify", tags=["Admin Payments"])
async def verify_payment(id: int):
    return {"msg": "Payment verified"}

@router.get("/refunds", tags=["Admin Payments"])
async def list_refunds():
    return []

@router.get("/refunds/{id}", tags=["Admin Payments"])
async def get_refund(id: int):
    return {"id": id, "status": "refunded"} 